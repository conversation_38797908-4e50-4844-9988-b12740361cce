using System;
using System.Drawing;
using System.Windows.Forms;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire d'ajout/modification d'article
    /// </summary>
    public partial class FrmArticleAddEdit : Form
    {
        private bool isEditMode = false;
        private string editCode;

        public FrmArticleAddEdit()
        {
            InitializeComponent();
            LoadComboBoxData();
            cmbStatut.SelectedIndex = 0; // Actif par défaut
        }

        public FrmArticleAddEdit(string code) : this()
        {
            isEditMode = true;
            editCode = code;
            this.Text = "Modifier l'article";
            titleLabel.Text = "✏️ Modifier l'article";
            LoadArticleData();
        }

        private void LoadComboBoxData()
        {
            // Charger les catégories
            cmbCategorie.Items.Clear();
            cmbCategorie.Items.AddRange(new[] {
                "Alimentaire", "Boissons", "Hygiène", "Électronique",
                "Textile", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"
            });

            // Charger les unités
            cmbUnite.Items.Clear();
            cmbUnite.Items.AddRange(new[] {
                "KG", "G", "L", "ML", "PCS", "BOX", "M", "CM"
            });
        }

        private void LoadArticleData()
        {
            if (isEditMode && !string.IsNullOrEmpty(editCode))
            {
                txtCode.Text = editCode;
                txtCode.ReadOnly = true;

                // Données fictives pour la démonstration
                switch (editCode)
                {
                    case "ART001":
                        txtNom.Text = "Coca Cola 1.5L";
                        cmbCategorie.SelectedItem = "Boissons";
                        cmbUnite.SelectedItem = "PCS";
                        txtPrixAchat.Text = "120,00";
                        txtPrixVente.Text = "150,00";
                        txtPrixVenteGros.Text = "140,00";
                        txtTVAAchat.Text = "19,00";
                        txtTVAVente.Text = "19,00";
                        txtStockMin.Text = "10";
                        txtStockMax.Text = "100";
                        txtStockActuel.Text = "50";
                        txtPiecesFardeau.Text = "24";
                        txtPrixFardeau.Text = "2880,00";
                        txtLotNumero.Text = "LOT2024001";
                        txtEmplacement.Text = "A1-B2-C3";
                        txtDescription.Text = "Boisson gazeuse Coca Cola 1.5L";
                        break;
                    default:
                        txtNom.Text = "Article exemple";
                        cmbCategorie.SelectedIndex = 0;
                        cmbUnite.SelectedIndex = 0;
                        txtPrixAchat.Text = "0,00";
                        txtPrixVente.Text = "0,00";
                        txtPrixVenteGros.Text = "0,00";
                        txtTVAAchat.Text = "19,00";
                        txtTVAVente.Text = "19,00";
                        txtStockMin.Text = "0";
                        txtStockMax.Text = "0";
                        txtStockActuel.Text = "0";
                        txtPiecesFardeau.Text = "1";
                        txtPrixFardeau.Text = "0,00";
                        txtLotNumero.Text = "";
                        txtEmplacement.Text = "";
                        break;
                }
                cmbStatut.SelectedItem = "Actif";
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                MessageBox.Show("Le code article est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtNom.Text))
            {
                MessageBox.Show("Le nom de l'article est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNom.Focus();
                return false;
            }

            if (cmbCategorie.SelectedIndex == -1)
            {
                MessageBox.Show("La catégorie est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbCategorie.Focus();
                return false;
            }

            if (cmbUnite.SelectedIndex == -1)
            {
                MessageBox.Show("L'unité de mesure est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbUnite.Focus();
                return false;
            }

            // Validation des prix
            if (!decimal.TryParse(txtPrixAchat.Text, out decimal prixAchat) || prixAchat < 0)
            {
                MessageBox.Show("Le prix d'achat unitaire doit être un nombre positif", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPrixAchat.Focus();
                return false;
            }

            if (!decimal.TryParse(txtPrixVente.Text, out decimal prixVente) || prixVente < 0)
            {
                MessageBox.Show("Le prix de vente unitaire doit être un nombre positif", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPrixVente.Focus();
                return false;
            }

            // Validation du prix de vente en gros (optionnel)
            if (!string.IsNullOrWhiteSpace(txtPrixVenteGros.Text))
            {
                if (!decimal.TryParse(txtPrixVenteGros.Text, out decimal prixVenteGros) || prixVenteGros < 0)
                {
                    MessageBox.Show("Le prix de vente en gros doit être un nombre positif", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPrixVenteGros.Focus();
                    return false;
                }
            }

            // Validation des TVA
            if (!decimal.TryParse(txtTVAAchat.Text, out decimal tvaAchat) || tvaAchat < 0 || tvaAchat > 100)
            {
                MessageBox.Show("La TVA achat doit être entre 0 et 100%", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtTVAAchat.Focus();
                return false;
            }

            if (!decimal.TryParse(txtTVAVente.Text, out decimal tvaVente) || tvaVente < 0 || tvaVente > 100)
            {
                MessageBox.Show("La TVA vente doit être entre 0 et 100%", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtTVAVente.Focus();
                return false;
            }

            // Validation des stocks
            if (!decimal.TryParse(txtStockMin.Text, out decimal stockMin) || stockMin < 0)
            {
                MessageBox.Show("Le stock minimum doit être un nombre positif", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtStockMin.Focus();
                return false;
            }

            if (!decimal.TryParse(txtStockMax.Text, out decimal stockMax) || stockMax < 0)
            {
                MessageBox.Show("Le stock maximum doit être un nombre positif", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtStockMax.Focus();
                return false;
            }

            if (stockMax <= stockMin)
            {
                MessageBox.Show("Le stock maximum doit être supérieur au stock minimum", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtStockMax.Focus();
                return false;
            }

            // Validation du stock actuel
            if (!decimal.TryParse(txtStockActuel.Text, out decimal stockActuel) || stockActuel < 0)
            {
                MessageBox.Show("Le stock actuel doit être un nombre positif", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtStockActuel.Focus();
                return false;
            }

            // Validation des pièces par fardeau
            if (!int.TryParse(txtPiecesFardeau.Text, out int piecesFardeau) || piecesFardeau <= 0)
            {
                MessageBox.Show("Le nombre de pièces par fardeau doit être supérieur à 0", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPiecesFardeau.Focus();
                return false;
            }

            // Validation du prix fardeau
            if (!decimal.TryParse(txtPrixFardeau.Text, out decimal prixFardeau) || prixFardeau < 0)
            {
                MessageBox.Show("Le prix du fardeau doit être un nombre positif", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPrixFardeau.Focus();
                return false;
            }

            return true;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateForm())
            {
                // Ici, vous ajouteriez la logique pour sauvegarder en base de données
                MessageBox.Show(
                    isEditMode ? "Article modifié avec succès" : "Article ajouté avec succès",
                    "Succès",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void TxtPrixAchat_TextChanged(object sender, EventArgs e)
        {
            // Calculer automatiquement le prix de vente avec une marge de 25%
            if (decimal.TryParse(txtPrixAchat.Text, out decimal prixAchat) && prixAchat > 0)
            {
                var prixVente = prixAchat * 1.25m;
                txtPrixVente.Text = prixVente.ToString("F2");

                // Calculer le prix de vente en gros (marge de 15%)
                var prixVenteGros = prixAchat * 1.15m;
                txtPrixVenteGros.Text = prixVenteGros.ToString("F2");

                // Calculer la marge bénéficiaire
                var marge = ((prixVente - prixAchat) / prixAchat) * 100;
                txtMarge.Text = marge.ToString("F2");
            }
        }

        private void TxtPiecesFardeau_TextChanged(object sender, EventArgs e)
        {
            // Calculer automatiquement le prix du fardeau
            if (decimal.TryParse(txtPrixVente.Text, out decimal prixVente) &&
                int.TryParse(txtPiecesFardeau.Text, out int pieces) &&
                prixVente > 0 && pieces > 0)
            {
                var prixFardeau = prixVente * pieces;
                txtPrixFardeau.Text = prixFardeau.ToString("F2");
            }
        }

        private void TxtPrixVente_TextChanged(object sender, EventArgs e)
        {
            // Recalculer le prix du fardeau si le prix de vente change
            TxtPiecesFardeau_TextChanged(sender, e);

            // Recalculer la marge
            if (decimal.TryParse(txtPrixAchat.Text, out decimal prixAchat) &&
                decimal.TryParse(txtPrixVente.Text, out decimal prixVente) &&
                prixAchat > 0 && prixVente > 0)
            {
                var marge = ((prixVente - prixAchat) / prixAchat) * 100;
                txtMarge.Text = marge.ToString("F2");
            }
        }

        private void FrmArticleAddEdit_Load(object sender, EventArgs e)
        {
            if (!isEditMode)
            {
                txtCode.Focus();
            }
            else
            {
                txtNom.Focus();
            }
        }
    }
}
